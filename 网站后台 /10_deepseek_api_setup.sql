-- DeepSeek API 数据库表结构
-- 创建时间: 2025-01-16

-- 1. DeepSeek API 配置表
CREATE TABLE IF NOT EXISTS `deepseek_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `is_enabled` TINYINT(1) DEFAULT 0 COMMENT '是否启用DeepSeek API',
    `selected_model` VARCHAR(100) DEFAULT 'deepseek-chat' COMMENT '选择的模型',
    `deep_thinking_enabled` TINYINT(1) DEFAULT 0 COMMENT '是否启用深度思考(R1)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DeepSeek API配置表';

-- 2. DeepSeek API 密钥表
CREATE TABLE IF NOT EXISTS `deepseek_api_keys` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `api_key` VARCHAR(255) NOT NULL COMMENT 'API密钥',
    `key_name` VARCHAR(100) DEFAULT NULL COMMENT '密钥名称/备注',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `last_used_at` TIMESTAMP NULL COMMENT '最后使用时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DeepSeek API密钥表';

-- 3. DeepSeek API 调用日志表
CREATE TABLE IF NOT EXISTS `deepseek_api_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `api_key_id` INT NOT NULL COMMENT '使用的API密钥ID',
    `model` VARCHAR(100) NOT NULL COMMENT '使用的模型',
    `request_data` TEXT COMMENT '请求数据',
    `response_data` TEXT COMMENT '响应数据',
    `status_code` INT DEFAULT NULL COMMENT 'HTTP状态码',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `response_time` FLOAT DEFAULT NULL COMMENT '响应时间(秒)',
    `tokens_used` INT DEFAULT NULL COMMENT '使用的token数量',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT '请求IP',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_api_key_id` (`api_key_id`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_status_code` (`status_code`),
    FOREIGN KEY (`api_key_id`) REFERENCES `deepseek_api_keys`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DeepSeek API调用日志表';

-- 插入默认配置
INSERT INTO `deepseek_config` (`is_enabled`, `selected_model`, `deep_thinking_enabled`) 
VALUES (0, 'deepseek-chat', 0) 
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;
