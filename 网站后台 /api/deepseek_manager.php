<?php
/**
 * DeepSeek API 管理后端
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入数据库配置
require_once '../xuxuemei/config/database.php';

/**
 * 响应JSON数据
 */
function sendResponse($success, $message = '', $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取DeepSeek配置
 */
function getConfig() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM deepseek_config ORDER BY id DESC LIMIT 1");
        $config = $stmt->fetch();
        
        if (!$config) {
            // 创建默认配置
            $stmt = $pdo->prepare("INSERT INTO deepseek_config (is_enabled, selected_model, deep_thinking_enabled) VALUES (0, 'deepseek-chat', 0)");
            $stmt->execute();
            $config = [
                'is_enabled' => 0,
                'selected_model' => 'deepseek-chat',
                'deep_thinking_enabled' => 0
            ];
        }
        
        return $config;
    } catch (PDOException $e) {
        error_log("获取DeepSeek配置失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 更新DeepSeek配置
 */
function updateConfig($data) {
    global $pdo;
    try {
        $is_enabled = isset($data['is_enabled']) ? (int)$data['is_enabled'] : 0;
        $selected_model = $data['selected_model'] ?? 'deepseek-chat';
        $deep_thinking_enabled = isset($data['deep_thinking_enabled']) ? (int)$data['deep_thinking_enabled'] : 0;
        
        // 检查是否存在配置
        $stmt = $pdo->query("SELECT COUNT(*) FROM deepseek_config");
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            $stmt = $pdo->prepare("UPDATE deepseek_config SET is_enabled = ?, selected_model = ?, deep_thinking_enabled = ?, updated_at = CURRENT_TIMESTAMP WHERE id = (SELECT id FROM (SELECT id FROM deepseek_config ORDER BY id DESC LIMIT 1) as tmp)");
        } else {
            $stmt = $pdo->prepare("INSERT INTO deepseek_config (is_enabled, selected_model, deep_thinking_enabled) VALUES (?, ?, ?)");
        }
        
        return $stmt->execute([$is_enabled, $selected_model, $deep_thinking_enabled]);
    } catch (PDOException $e) {
        error_log("更新DeepSeek配置失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取API密钥列表
 */
function getApiKeys() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT id, api_key, key_name, is_active, usage_count, last_used_at, created_at FROM deepseek_api_keys ORDER BY created_at DESC");
        $keys = $stmt->fetchAll();
        
        // 隐藏部分密钥内容
        foreach ($keys as &$key) {
            $key['api_key_masked'] = substr($key['api_key'], 0, 8) . '...' . substr($key['api_key'], -4);
        }
        
        return $keys;
    } catch (PDOException $e) {
        error_log("获取API密钥失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 添加API密钥
 */
function addApiKey($api_key, $key_name = null) {
    global $pdo;
    try {
        // 验证API密钥格式
        if (empty($api_key) || !preg_match('/^sk-[a-zA-Z0-9]{32,}$/', $api_key)) {
            return ['success' => false, 'message' => 'API密钥格式不正确'];
        }
        
        // 检查是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM deepseek_api_keys WHERE api_key = ?");
        $stmt->execute([$api_key]);
        if ($stmt->fetchColumn() > 0) {
            return ['success' => false, 'message' => 'API密钥已存在'];
        }
        
        $stmt = $pdo->prepare("INSERT INTO deepseek_api_keys (api_key, key_name) VALUES (?, ?)");
        $result = $stmt->execute([$api_key, $key_name]);
        
        return ['success' => $result, 'message' => $result ? 'API密钥添加成功' : 'API密钥添加失败'];
    } catch (PDOException $e) {
        error_log("添加API密钥失败: " . $e->getMessage());
        return ['success' => false, 'message' => '添加API密钥失败: ' . $e->getMessage()];
    }
}

/**
 * 删除API密钥
 */
function deleteApiKey($key_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("DELETE FROM deepseek_api_keys WHERE id = ?");
        $result = $stmt->execute([$key_id]);
        
        return ['success' => $result, 'message' => $result ? 'API密钥删除成功' : 'API密钥删除失败'];
    } catch (PDOException $e) {
        error_log("删除API密钥失败: " . $e->getMessage());
        return ['success' => false, 'message' => '删除API密钥失败: ' . $e->getMessage()];
    }
}

/**
 * 获取随机可用的API密钥
 */
function getRandomApiKey() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT api_key FROM deepseek_api_keys WHERE is_active = 1 ORDER BY RAND() LIMIT 1");
        $result = $stmt->fetch();
        return $result ? $result['api_key'] : null;
    } catch (PDOException $e) {
        error_log("获取随机API密钥失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 更新API密钥使用统计
 */
function updateKeyUsage($api_key) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE deepseek_api_keys SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP WHERE api_key = ?");
        return $stmt->execute([$api_key]);
    } catch (PDOException $e) {
        error_log("更新API密钥使用统计失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录API调用日志
 */
function logApiCall($api_key, $model, $request_data, $response_data, $status_code, $error_message = null, $response_time = null, $tokens_used = null) {
    global $pdo;
    try {
        // 获取API密钥ID
        $stmt = $pdo->prepare("SELECT id FROM deepseek_api_keys WHERE api_key = ?");
        $stmt->execute([$api_key]);
        $key_result = $stmt->fetch();
        
        if (!$key_result) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO deepseek_api_logs 
            (api_key_id, model, request_data, response_data, status_code, error_message, response_time, tokens_used, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $key_result['id'],
            $model,
            json_encode($request_data, JSON_UNESCAPED_UNICODE),
            json_encode($response_data, JSON_UNESCAPED_UNICODE),
            $status_code,
            $error_message,
            $response_time,
            $tokens_used,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (PDOException $e) {
        error_log("记录API调用日志失败: " . $e->getMessage());
        return false;
    }
}

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'config':
                    $config = getConfig();
                    if ($config !== false) {
                        sendResponse(true, '获取配置成功', $config);
                    } else {
                        sendResponse(false, '获取配置失败');
                    }
                    break;
                    
                case 'keys':
                    $keys = getApiKeys();
                    if ($keys !== false) {
                        sendResponse(true, '获取API密钥列表成功', $keys);
                    } else {
                        sendResponse(false, '获取API密钥列表失败');
                    }
                    break;
                    
                case 'random_key':
                    $key = getRandomApiKey();
                    if ($key) {
                        sendResponse(true, '获取随机API密钥成功', ['api_key' => $key]);
                    } else {
                        sendResponse(false, '没有可用的API密钥');
                    }
                    break;
                    
                default:
                    sendResponse(false, '无效的操作');
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'update_config':
                    $result = updateConfig($input);
                    if ($result) {
                        sendResponse(true, '配置更新成功');
                    } else {
                        sendResponse(false, '配置更新失败');
                    }
                    break;
                    
                case 'add_key':
                    $result = addApiKey($input['api_key'] ?? '', $input['key_name'] ?? null);
                    sendResponse($result['success'], $result['message']);
                    break;
                    
                case 'delete_key':
                    $result = deleteApiKey($input['key_id'] ?? 0);
                    sendResponse($result['success'], $result['message']);
                    break;
                    
                case 'log_call':
                    $result = logApiCall(
                        $input['api_key'] ?? '',
                        $input['model'] ?? '',
                        $input['request_data'] ?? [],
                        $input['response_data'] ?? [],
                        $input['status_code'] ?? 0,
                        $input['error_message'] ?? null,
                        $input['response_time'] ?? null,
                        $input['tokens_used'] ?? null
                    );
                    sendResponse($result, $result ? '日志记录成功' : '日志记录失败');
                    break;
                    
                default:
                    sendResponse(false, '无效的操作');
            }
            break;
            
        default:
            sendResponse(false, '不支持的请求方法');
    }
} catch (Exception $e) {
    error_log("DeepSeek API管理错误: " . $e->getMessage());
    sendResponse(false, '服务器内部错误: ' . $e->getMessage());
}
?>
