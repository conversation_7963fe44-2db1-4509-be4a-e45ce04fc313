<?php
/**
 * DeepSeek API 独立接口
 * 提供与DeepSeek AI模型的直接对接功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入数据库配置
require_once '../xuxuemei/config/database.php';

/**
 * 响应JSON数据
 */
function sendResponse($success, $message = '', $data = null, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取DeepSeek配置
 */
function getDeepSeekConfig() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM deepseek_config WHERE is_enabled = 1 ORDER BY id DESC LIMIT 1");
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("获取DeepSeek配置失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取随机可用的API密钥
 */
function getRandomApiKey() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT api_key FROM deepseek_api_keys WHERE is_active = 1 ORDER BY RAND() LIMIT 1");
        $result = $stmt->fetch();
        return $result ? $result['api_key'] : null;
    } catch (PDOException $e) {
        error_log("获取随机API密钥失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 更新API密钥使用统计
 */
function updateKeyUsage($api_key) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE deepseek_api_keys SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP WHERE api_key = ?");
        return $stmt->execute([$api_key]);
    } catch (PDOException $e) {
        error_log("更新API密钥使用统计失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录API调用日志
 */
function logApiCall($api_key, $model, $request_data, $response_data, $status_code, $error_message = null, $response_time = null, $tokens_used = null) {
    global $pdo;
    try {
        // 获取API密钥ID
        $stmt = $pdo->prepare("SELECT id FROM deepseek_api_keys WHERE api_key = ?");
        $stmt->execute([$api_key]);
        $key_result = $stmt->fetch();
        
        if (!$key_result) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO deepseek_api_logs 
            (api_key_id, model, request_data, response_data, status_code, error_message, response_time, tokens_used, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $key_result['id'],
            $model,
            json_encode($request_data, JSON_UNESCAPED_UNICODE),
            json_encode($response_data, JSON_UNESCAPED_UNICODE),
            $status_code,
            $error_message,
            $response_time,
            $tokens_used,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (PDOException $e) {
        error_log("记录API调用日志失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 调用DeepSeek API
 */
function callDeepSeekAPI($api_key, $model, $messages, $options = []) {
    $start_time = microtime(true);
    
    // DeepSeek API 端点
    $url = 'https://api.deepseek.com/v1/chat/completions';
    
    // 构建请求数据
    $request_data = [
        'model' => $model,
        'messages' => $messages,
        'max_tokens' => $options['max_tokens'] ?? 2048,
        'temperature' => $options['temperature'] ?? 0.7,
        'top_p' => $options['top_p'] ?? 0.9,
        'stream' => false
    ];
    
    // 如果是R1模型，添加深度思考参数
    if (strpos($model, 'r1') !== false && isset($options['deep_thinking']) && $options['deep_thinking']) {
        $request_data['reasoning_effort'] = 'medium';
    }
    
    // 设置请求头
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key,
        'User-Agent: XiaoMeiHua-AI-Client/1.0'
    ];
    
    // 初始化cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($request_data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    $response_time = microtime(true) - $start_time;
    
    // 处理cURL错误
    if ($curl_error) {
        $error_message = "cURL错误: " . $curl_error;
        logApiCall($api_key, $model, $request_data, null, 0, $error_message, $response_time);
        return [
            'success' => false,
            'error' => $error_message,
            'response_time' => $response_time
        ];
    }
    
    // 解析响应
    $response_data = json_decode($response, true);
    
    if ($http_code === 200 && $response_data) {
        // 成功响应
        $tokens_used = $response_data['usage']['total_tokens'] ?? null;
        
        // 更新密钥使用统计
        updateKeyUsage($api_key);
        
        // 记录日志
        logApiCall($api_key, $model, $request_data, $response_data, $http_code, null, $response_time, $tokens_used);
        
        return [
            'success' => true,
            'data' => $response_data,
            'response_time' => $response_time,
            'tokens_used' => $tokens_used
        ];
    } else {
        // 错误响应
        $error_message = $response_data['error']['message'] ?? "HTTP错误: $http_code";
        
        // 记录日志
        logApiCall($api_key, $model, $request_data, $response_data, $http_code, $error_message, $response_time);
        
        return [
            'success' => false,
            'error' => $error_message,
            'http_code' => $http_code,
            'response_time' => $response_time,
            'response_data' => $response_data
        ];
    }
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, '仅支持POST请求', null, 405);
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, '无效的JSON数据', null, 400);
}

try {
    // 检查DeepSeek是否启用
    $config = getDeepSeekConfig();
    if (!$config) {
        sendResponse(false, 'DeepSeek API未启用或配置错误', null, 503);
    }
    
    // 获取随机API密钥
    $api_key = getRandomApiKey();
    if (!$api_key) {
        sendResponse(false, '没有可用的API密钥', null, 503);
    }
    
    // 验证必需参数
    if (!isset($input['messages']) || !is_array($input['messages'])) {
        sendResponse(false, '缺少messages参数或格式错误', null, 400);
    }
    
    // 获取模型
    $model = $input['model'] ?? $config['selected_model'];
    
    // 构建选项
    $options = [
        'max_tokens' => $input['max_tokens'] ?? 2048,
        'temperature' => $input['temperature'] ?? 0.7,
        'top_p' => $input['top_p'] ?? 0.9,
        'deep_thinking' => $config['deep_thinking_enabled'] && ($input['deep_thinking'] ?? false)
    ];
    
    // 调用DeepSeek API
    $result = callDeepSeekAPI($api_key, $model, $input['messages'], $options);
    
    if ($result['success']) {
        sendResponse(true, '请求成功', [
            'response' => $result['data'],
            'response_time' => $result['response_time'],
            'tokens_used' => $result['tokens_used'],
            'model' => $model
        ]);
    } else {
        sendResponse(false, $result['error'], [
            'response_time' => $result['response_time'],
            'http_code' => $result['http_code'] ?? null,
            'model' => $model
        ], $result['http_code'] ?? 500);
    }
    
} catch (Exception $e) {
    error_log("DeepSeek API接口错误: " . $e->getMessage());
    sendResponse(false, '服务器内部错误: ' . $e->getMessage(), null, 500);
}

// 如果是GET请求，显示API文档
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>DeepSeek API 接口文档</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .endpoint { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .method { background: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
            .example { margin: 15px 0; }
        </style>
    </head>
    <body>
        <h1>DeepSeek API 接口文档</h1>

        <div class="endpoint">
            <h3><span class="method">POST</span> /api/deepseek_api.php</h3>
            <p>调用DeepSeek AI模型进行对话</p>

            <h4>请求参数：</h4>
            <pre>{
    "messages": [
        {
            "role": "user",
            "content": "你好，请介绍一下自己"
        }
    ],
    "model": "deepseek-chat",  // 可选，默认使用配置中的模型
    "max_tokens": 2048,        // 可选，默认2048
    "temperature": 0.7,        // 可选，默认0.7
    "top_p": 0.9,             // 可选，默认0.9
    "deep_thinking": false     // 可选，是否启用深度思考
}</pre>

            <h4>响应示例：</h4>
            <pre>{
    "success": true,
    "message": "请求成功",
    "data": {
        "response": {
            "id": "chatcmpl-xxx",
            "object": "chat.completion",
            "created": 1234567890,
            "model": "deepseek-chat",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "你好！我是DeepSeek，一个AI助手..."
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            }
        },
        "response_time": 1.23,
        "tokens_used": 30,
        "model": "deepseek-chat"
    },
    "timestamp": "2025-01-16 12:00:00"
}</pre>
        </div>

        <div class="example">
            <h3>使用示例</h3>
            <pre>curl -X POST "<?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>" \
     -H "Content-Type: application/json" \
     -d '{
       "messages": [
         {
           "role": "user",
           "content": "你好，请介绍一下自己"
         }
       ]
     }'</pre>
        </div>

        <div class="example">
            <h3>JavaScript示例</h3>
            <pre>fetch('<?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        messages: [
            {
                role: 'user',
                content: '你好，请介绍一下自己'
            }
        ]
    })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));</pre>
        </div>

        <p><strong>注意：</strong>使用此API前，请确保在后台管理系统中已配置DeepSeek API密钥并启用服务。</p>
    </body>
    </html>
    <?php
    exit;
}
?>
