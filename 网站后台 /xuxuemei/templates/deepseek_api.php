<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 加载数据库连接
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 获取DeepSeek配置
function getDeepSeekConfig() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM deepseek_config ORDER BY id DESC LIMIT 1");
        $config = $stmt->fetch();
        if (!$config) {
            // 如果没有配置，创建默认配置
            $stmt = $pdo->prepare("INSERT INTO deepseek_config (is_enabled, selected_model, deep_thinking_enabled) VALUES (0, 'deepseek-chat', 0)");
            $stmt->execute();
            return [
                'is_enabled' => 0,
                'selected_model' => 'deepseek-chat',
                'deep_thinking_enabled' => 0
            ];
        }
        return $config;
    } catch (PDOException $e) {
        error_log("获取DeepSeek配置失败: " . $e->getMessage());
        return [
            'is_enabled' => 0,
            'selected_model' => 'deepseek-chat',
            'deep_thinking_enabled' => 0
        ];
    }
}

// 获取API密钥列表
function getApiKeys() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM deepseek_api_keys ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("获取API密钥失败: " . $e->getMessage());
        return [];
    }
}

// 处理表单提交
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_config'])) {
        // 保存配置
        try {
            $is_enabled = isset($_POST['is_enabled']) ? 1 : 0;
            $selected_model = $_POST['selected_model'] ?? 'deepseek-chat';
            $deep_thinking_enabled = isset($_POST['deep_thinking_enabled']) ? 1 : 0;
            
            $stmt = $pdo->prepare("UPDATE deepseek_config SET is_enabled = ?, selected_model = ?, deep_thinking_enabled = ? WHERE id = (SELECT id FROM (SELECT id FROM deepseek_config ORDER BY id DESC LIMIT 1) as tmp)");
            $stmt->execute([$is_enabled, $selected_model, $deep_thinking_enabled]);
            
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> 配置已保存</div>';
        } catch (PDOException $e) {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 保存配置失败: ' . $e->getMessage() . '</div>';
        }
    } elseif (isset($_POST['add_api_key'])) {
        // 添加API密钥
        try {
            $api_key = trim($_POST['api_key']);
            $key_name = trim($_POST['key_name']) ?: null;
            
            if (!empty($api_key)) {
                $stmt = $pdo->prepare("INSERT INTO deepseek_api_keys (api_key, key_name) VALUES (?, ?)");
                $stmt->execute([$api_key, $key_name]);
                $message = '<div class="message success"><i class="fas fa-check-circle"></i> API密钥已添加</div>';
            } else {
                $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> API密钥不能为空</div>';
            }
        } catch (PDOException $e) {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 添加API密钥失败: ' . $e->getMessage() . '</div>';
        }
    } elseif (isset($_POST['delete_api_key'])) {
        // 删除API密钥
        try {
            $key_id = $_POST['key_id'];
            $stmt = $pdo->prepare("DELETE FROM deepseek_api_keys WHERE id = ?");
            $stmt->execute([$key_id]);
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> API密钥已删除</div>';
        } catch (PDOException $e) {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 删除API密钥失败: ' . $e->getMessage() . '</div>';
        }
    }
}

// 获取当前配置和密钥
$config = getDeepSeekConfig();
$apiKeys = getApiKeys();
?>

<div class="deepseek-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-robot"></i> DeepSeek API</h1>
        <p>管理DeepSeek AI模型配置和API密钥</p>
    </div>

    <?php echo $message; ?>

    <!-- 主配置区域 -->
    <div class="config-section">
        <form method="POST" action="">
            <div class="config-card">
                <h2><i class="fas fa-cogs"></i> 基本配置</h2>
                
                <!-- 启用AI回复开关 -->
                <div class="form-group">
                    <div class="toggle-container">
                        <label class="toggle-switch">
                            <input type="checkbox" name="is_enabled" value="1" <?php echo $config['is_enabled'] ? 'checked' : ''; ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">启用AI回复</span>
                    </div>
                </div>

                <!-- API密钥输入 -->
                <div class="form-group">
                    <label for="api_key">API密钥:</label>
                    <div class="input-group">
                        <input type="text" id="api_key" name="api_key" placeholder="输入DeepSeek API密钥" class="form-control">
                        <input type="text" name="key_name" placeholder="密钥备注(可选)" class="form-control">
                        <button type="submit" name="add_api_key" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 保存
                        </button>
                    </div>
                </div>

                <!-- 模型选择 -->
                <div class="form-group">
                    <label for="selected_model">模型:</label>
                    <select id="selected_model" name="selected_model" class="form-control">
                        <option value="deepseek-chat" <?php echo $config['selected_model'] === 'deepseek-chat' ? 'selected' : ''; ?>>DeepSeek Chat</option>
                        <option value="deepseek-coder" <?php echo $config['selected_model'] === 'deepseek-coder' ? 'selected' : ''; ?>>DeepSeek Coder</option>
                        <option value="deepseek-reasoner" <?php echo $config['selected_model'] === 'deepseek-reasoner' ? 'selected' : ''; ?>>DeepSeek Reasoner</option>
                    </select>
                </div>

                <!-- 深度思考开关 -->
                <div class="form-group">
                    <div class="toggle-container">
                        <label class="toggle-switch">
                            <input type="checkbox" name="deep_thinking_enabled" value="1" <?php echo $config['deep_thinking_enabled'] ? 'checked' : ''; ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">深度思考 (R1)</span>
                        <small class="help-text">仅对DeepSeek-R1-0528模型有效</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="save_config" class="btn btn-success">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- API密钥管理 -->
    <div class="keys-section">
        <div class="config-card">
            <h2><i class="fas fa-key"></i> API密钥管理</h2>
            
            <?php if (!empty($apiKeys)): ?>
                <div class="keys-list">
                    <?php foreach ($apiKeys as $key): ?>
                        <div class="key-item">
                            <div class="key-info">
                                <div class="key-value">
                                    <span class="key-text"><?php echo substr($key['api_key'], 0, 20) . '...'; ?></span>
                                    <?php if ($key['key_name']): ?>
                                        <span class="key-name">(<?php echo htmlspecialchars($key['key_name']); ?>)</span>
                                    <?php endif; ?>
                                </div>
                                <div class="key-meta">
                                    <span class="usage-count">使用次数: <?php echo $key['usage_count']; ?></span>
                                    <span class="created-date">创建时间: <?php echo date('Y-m-d H:i', strtotime($key['created_at'])); ?></span>
                                </div>
                            </div>
                            <div class="key-actions">
                                <form method="POST" action="" style="display: inline;">
                                    <input type="hidden" name="key_id" value="<?php echo $key['id']; ?>">
                                    <button type="submit" name="delete_api_key" class="btn btn-danger btn-sm" onclick="return confirm('确定要删除这个API密钥吗？')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-key"></i>
                    <p>暂无API密钥，请添加密钥以开始使用</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- DeepSeek API专用样式 -->
<style>
.deepseek-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

.config-section, .keys-section {
    margin-bottom: 30px;
}

.config-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.config-card h2 {
    color: white;
    margin-bottom: 25px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: white;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-control:focus {
    outline: none;
    border-color: #ff6b9d;
    box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.input-group .form-control {
    flex: 1;
}

.toggle-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 54px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 28px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4ade80;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.help-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 5px;
    line-height: 1.4;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c, #ff4b2b);
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.form-actions {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.keys-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.key-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.key-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.key-info {
    flex: 1;
}

.key-value {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.key-text {
    color: white;
    font-family: monospace;
    font-size: 14px;
    font-weight: 500;
}

.key-name {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.key-meta {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.message.success {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #4ade80;
}

.message.error {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #f87171;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .deepseek-container {
        padding: 15px;
    }

    .input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .key-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .key-meta {
        flex-direction: column;
        gap: 5px;
    }
}
</style>
