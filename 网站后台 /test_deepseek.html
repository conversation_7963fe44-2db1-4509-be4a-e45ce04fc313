<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #ff6b9d;
            box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
        }
        
        button {
            background: linear-gradient(135deg, #11998e, #38ef7d);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.3);
            color: #f87171;
        }
        
        .success {
            background: rgba(34, 197, 94, 0.2);
            border-color: rgba(34, 197, 94, 0.3);
            color: #4ade80;
        }
        
        .loading {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .meta-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 DeepSeek API 测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="message">消息内容:</label>
                <textarea id="message" rows="4" placeholder="请输入您想要发送给AI的消息..." required>你好，请介绍一下自己</textarea>
            </div>
            
            <div class="form-group">
                <label for="model">模型选择:</label>
                <select id="model">
                    <option value="">使用默认模型</option>
                    <option value="deepseek-chat">DeepSeek Chat</option>
                    <option value="deepseek-coder">DeepSeek Coder</option>
                    <option value="deepseek-reasoner">DeepSeek Reasoner</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="temperature">Temperature (0-1):</label>
                <input type="number" id="temperature" min="0" max="1" step="0.1" value="0.7" placeholder="0.7">
            </div>
            
            <div class="form-group">
                <label for="maxTokens">最大Token数:</label>
                <input type="number" id="maxTokens" min="1" max="4096" value="2048" placeholder="2048">
            </div>
            
            <button type="submit" id="submitBtn">发送请求</button>
        </form>
        
        <div id="response" class="response" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const responseDiv = document.getElementById('response');
            
            // 获取表单数据
            const message = document.getElementById('message').value.trim();
            const model = document.getElementById('model').value;
            const temperature = parseFloat(document.getElementById('temperature').value);
            const maxTokens = parseInt(document.getElementById('maxTokens').value);
            
            if (!message) {
                alert('请输入消息内容');
                return;
            }
            
            // 禁用按钮并显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '发送中...';
            responseDiv.style.display = 'block';
            responseDiv.className = 'response loading';
            responseDiv.textContent = '正在调用DeepSeek API，请稍候...';
            
            try {
                // 构建请求数据
                const requestData = {
                    messages: [
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    temperature: temperature,
                    max_tokens: maxTokens
                };
                
                if (model) {
                    requestData.model = model;
                }
                
                // 发送请求
                const response = await fetch('./api/deepseek_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    responseDiv.className = 'response success';
                    const aiResponse = result.data.response.choices[0].message.content;
                    const metaInfo = `
模型: ${result.data.model}
响应时间: ${result.data.response_time.toFixed(2)}秒
使用Token: ${result.data.tokens_used}
时间: ${result.timestamp}`;
                    
                    responseDiv.innerHTML = `<strong>AI回复:</strong><br>${aiResponse}<div class="meta-info">${metaInfo}</div>`;
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `错误: ${result.message}`;
                }
                
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.textContent = `请求失败: ${error.message}`;
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = '发送请求';
            }
        });
    </script>
</body>
</html>
