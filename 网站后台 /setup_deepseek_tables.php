<?php
/**
 * DeepSeek API 数据库表创建脚本
 */

// 引入数据库配置
require_once 'xuxuemei/config/database.php';

try {
    echo "开始创建DeepSeek API数据库表...\n";
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/deepseek_api_setup.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    $statements = explode(';', $sql);

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            echo "执行SQL: " . substr($statement, 0, 50) . "...\n";
            try {
                $pdo->exec($statement);
                echo "✅ 执行成功\n";
            } catch (Exception $e) {
                echo "⚠️ 执行警告: " . $e->getMessage() . "\n";
            }
        }
    }
    echo "✅ DeepSeek API数据库表创建成功！\n";
    
    // 验证表是否创建成功
    $tables = ['deepseek_config', 'deepseek_api_keys', 'deepseek_api_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ 表 $table 创建成功\n";
        } else {
            echo "❌ 表 $table 创建失败\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
