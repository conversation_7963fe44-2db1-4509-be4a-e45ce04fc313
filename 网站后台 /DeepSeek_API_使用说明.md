# DeepSeek API 功能使用说明

## 功能概述

本系统已成功集成DeepSeek AI模型，提供完整的API配置和调用功能。

## 主要功能

### 1. 后台管理界面

- **位置**: 后台管理 → APP客服 (侧边栏菜单)
- **功能**: 
  - 启用/禁用AI回复开关
  - 模型选择下拉框 (DeepSeek Chat, DeepSeek Coder, DeepSeek Reasoner)
  - API密钥管理 (添加、删除、查看使用统计)
  - 深度思考(R1)开关

### 2. API密钥管理

- **添加密钥**: 在配置页面输入API密钥和可选的备注名称
- **随机调用**: 系统会随机选择可用的API密钥进行调用
- **使用统计**: 自动记录每个密钥的使用次数和最后使用时间
- **删除管理**: 可以删除不需要的API密钥

### 3. 独立API接口

- **接口地址**: `/api/deepseek_api.php`
- **请求方法**: POST
- **功能**: 直接调用DeepSeek API，支持所有模型和参数

## 使用步骤

### 第一步：配置API密钥

1. 登录后台管理系统
2. 点击侧边栏"APP客服"菜单
3. 在"API密钥"输入框中输入您的DeepSeek API密钥
4. 可选择添加备注名称
5. 点击"保存"按钮

### 第二步：配置基本设置

1. 开启"启用AI回复"开关
2. 选择合适的模型 (推荐DeepSeek Chat)
3. 根据需要开启"深度思考(R1)"功能
4. 点击"保存配置"

### 第三步：测试功能

1. 访问测试页面: `/test_deepseek.html`
2. 输入测试消息
3. 选择模型和参数
4. 点击"发送请求"测试

## API接口使用

### 请求示例

```bash
curl -X POST "http://your-domain.com/api/deepseek_api.php" \
     -H "Content-Type: application/json" \
     -d '{
       "messages": [
         {
           "role": "user", 
           "content": "你好，请介绍一下自己"
         }
       ],
       "model": "deepseek-chat",
       "temperature": 0.7,
       "max_tokens": 2048
     }'
```

### 响应示例

```json
{
    "success": true,
    "message": "请求成功",
    "data": {
        "response": {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "你好！我是DeepSeek，一个AI助手..."
                    }
                }
            ],
            "usage": {
                "total_tokens": 30
            }
        },
        "response_time": 1.23,
        "tokens_used": 30,
        "model": "deepseek-chat"
    },
    "timestamp": "2025-01-16 12:00:00"
}
```

## 数据库表结构

系统自动创建了以下数据库表：

1. **deepseek_config**: 存储DeepSeek配置信息
2. **deepseek_api_keys**: 存储API密钥信息
3. **deepseek_api_logs**: 记录API调用日志

## 安全特性

- API密钥加密存储
- 调用日志记录
- 错误处理和重试机制
- 随机密钥轮换使用

## 注意事项

1. 确保DeepSeek API密钥有效且有足够余额
2. 建议添加多个API密钥以提高可用性
3. 定期检查API调用日志和使用统计
4. 根据实际需求调整模型参数

## 故障排除

1. **API调用失败**: 检查密钥是否有效，网络是否正常
2. **配置无法保存**: 检查数据库连接和权限
3. **页面无法访问**: 确认文件路径和服务器配置

## 技术支持

如有问题，请检查：
- 服务器错误日志
- 数据库连接状态
- API密钥余额和有效性
